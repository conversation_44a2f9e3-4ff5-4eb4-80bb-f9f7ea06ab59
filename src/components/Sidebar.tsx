'use client'

import { KnowledgeCards } from './KnowledgeCards'
import { AIChat } from './AIChat'

interface KnowledgeCard {
  title: string
  content: string
}

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
}

interface SidebarProps {
  knowledgeCards: KnowledgeCard[]
  knowledgeCardsLoading: boolean
  chatMessages: ChatMessage[]
  chatLoading: boolean
  onSaveCard: (card: KnowledgeCard) => Promise<void>
  onSendMessage: (message: string) => void
}

export function Sidebar({
  knowledgeCards,
  knowledgeCardsLoading,
  chatMessages,
  chatLoading,
  onSaveCard,
  onSendMessage,
}: SidebarProps) {
  return (
    <div className="h-full flex flex-col">
      {/* 知识卡片区域 */}
      <div className="flex-1 border-b border-gray-200">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-sm font-semibold text-gray-900">知识卡片</h3>
          <p className="text-xs text-gray-600 mt-1">
            {knowledgeCards.length} 张卡片
          </p>
        </div>
        <div className="flex-1 overflow-y-auto p-4">
          <KnowledgeCards
            cards={knowledgeCards}
            loading={knowledgeCardsLoading}
            onSaveCard={onSaveCard}
          />
        </div>
      </div>

      {/* AI 聊天区域 */}
      <div className="h-96 flex flex-col">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-sm font-semibold text-gray-900">AI 助手</h3>
          <p className="text-xs text-gray-600 mt-1">
            基于文章内容的智能问答
          </p>
        </div>
        <div className="flex-1">
          <AIChat
            messages={chatMessages}
            loading={chatLoading}
            onSendMessage={onSendMessage}
          />
        </div>
      </div>
    </div>
  )
} 