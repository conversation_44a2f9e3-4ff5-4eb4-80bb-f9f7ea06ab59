'use client'

import { useState } from 'react'
import { Send, Paperclip } from 'lucide-react'

interface WelcomeScreenProps {
  onSubmit: (input: string) => void
  loading: boolean
}

export function WelcomeScreen({ onSubmit, loading }: WelcomeScreenProps) {
  const [input, setInput] = useState('')

  const handleSubmit = () => {
    if (input.trim()) {
      onSubmit(input.trim())
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !loading) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-semibold text-sm">知</span>
          </div>
          <span className="font-semibold text-gray-900">知识卡片</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center px-4">
        <div className="w-full max-w-3xl">
          {/* Welcome Message */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-semibold text-gray-900 mb-4">
              有什么可以帮您的？
            </h1>
            <p className="text-gray-600">
              输入网页链接或文章内容，我将为您生成结构化的知识卡片
            </p>
          </div>

          {/* Input Area */}
          <div className="relative">
            <div className="relative flex items-end bg-white border border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex-1 min-h-[52px] max-h-[200px]">
                <textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  disabled={loading}
                  placeholder="输入网页链接或粘贴文章内容..."
                  className="w-full h-full min-h-[52px] max-h-[200px] px-4 py-3 bg-transparent border-0 resize-none outline-none placeholder:text-gray-500 text-gray-900"
                  style={{ lineHeight: '1.5' }}
                />
              </div>

              <div className="flex items-center space-x-2 p-2">
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200 rounded-lg hover:bg-gray-100"
                  disabled={loading}
                >
                  <Paperclip className="w-4 h-4" />
                </button>

                <button
                  onClick={handleSubmit}
                  disabled={!input.trim() || loading}
                  className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Example Prompts */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-3">
            <button
              onClick={() => setInput('https://example.com/article')}
              className="p-4 text-left bg-gray-50 hover:bg-gray-100 rounded-xl border border-gray-200 transition-colors duration-200"
              disabled={loading}
            >
              <div className="font-medium text-gray-900 mb-1">分析网页文章</div>
              <div className="text-sm text-gray-600">输入网页链接，自动提取并分析内容</div>
            </button>

            <button
              onClick={() => setInput('请帮我分析这篇文章的主要观点...')}
              className="p-4 text-left bg-gray-50 hover:bg-gray-100 rounded-xl border border-gray-200 transition-colors duration-200"
              disabled={loading}
            >
              <div className="font-medium text-gray-900 mb-1">文本内容分析</div>
              <div className="text-sm text-gray-600">直接粘贴文章内容进行分析</div>
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 text-center">
        <p className="text-xs text-gray-500">
          知识卡片可以帮您整理和分析各种文本内容
        </p>
      </div>
    </div>
  )
}