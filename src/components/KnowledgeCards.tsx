'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Save, Check, Copy } from 'lucide-react'

interface KnowledgeCard {
  title: string
  content: string
}

interface KnowledgeCardsProps {
  cards: KnowledgeCard[]
  loading: boolean
  onSaveCard: (card: KnowledgeCard) => Promise<void>
}

function CardSkeleton() {
  return (
    <Card className="animate-pulse">
      <CardHeader>
        <div className="skeleton h-5 w-3/4"></div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="skeleton h-4 w-full"></div>
        <div className="skeleton h-4 w-full"></div>
        <div className="skeleton h-4 w-2/3"></div>
        <div className="mt-4">
          <div className="skeleton h-8 w-20"></div>
        </div>
      </CardContent>
    </Card>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <CardSkeleton key={i} />
      ))}
    </div>
  )
}

interface SaveableCardProps {
  card: KnowledgeCard
  onSave: (card: KnowledgeCard) => Promise<void>
}

function SaveableCard({ card, onSave }: SaveableCardProps) {
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)

  const handleSave = async () => {
    setSaving(true)
    try {
      await onSave(card)
      setSaved(true)
      // 3秒后重置状态
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(`${card.title}\n\n${card.content}`)
      // 可以添加toast提示
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  return (
    <Card className="group hover:shadow-md transition-all duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-base font-semibold leading-tight text-foreground">
            {card.title}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            title="复制内容"
          >
            <Copy className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground leading-relaxed mb-4">
          {card.content}
        </p>
        <Button
          onClick={handleSave}
          disabled={saving || saved}
          size="sm"
          className={`transition-all duration-200 ${
            saved 
              ? 'bg-green-500 hover:bg-green-500 text-white' 
              : 'hover:shadow-sm'
          }`}
        >
          {saving ? (
            <>
              <div className="mr-2 w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              保存中...
            </>
          ) : saved ? (
            <>
              <Check className="mr-2 h-4 w-4" />
              已保存
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              保存
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}

export function KnowledgeCards({ cards, loading, onSaveCard }: KnowledgeCardsProps) {
  if (loading) {
    return <LoadingSkeleton />
  }

  if (cards.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <div className="text-4xl text-muted-foreground/30">💡</div>
        <div className="text-center">
          <p className="font-medium text-muted-foreground">暂无知识卡片</p>
          <p className="text-sm text-muted-foreground/60 mt-2">
            处理内容后将自动生成
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {cards.map((card, index) => (
        <SaveableCard
          key={index}
          card={card}
          onSave={onSaveCard}
        />
      ))}
    </div>
  )
} 