@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --radius: 0.625rem;
  --background: #FFFFFF;
  --foreground: #1F2937;
  --card: #FFFFFF;
  --card-foreground: #1F2937;
  --popover: #FFFFFF;
  --popover-foreground: #1F2937;
  --primary: #3B82F6;
  --primary-foreground: #FFFFFF;
  --secondary: #F9FAFB;
  --secondary-foreground: #1F2937;
  --muted: #F9FAFB;
  --muted-foreground: #6B7280;
  --accent: #F9FAFB;
  --accent-foreground: #1F2937;
  --destructive: #EF4444;
  --border: #E5E7EB;
  --input: #E5E7EB;
  --ring: #3B82F6;
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: #F9FAFB;
  --sidebar-foreground: #1F2937;
  --sidebar-primary: #3B82F6;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #F3F4F6;
  --sidebar-accent-foreground: #1F2937;
  --sidebar-border: #E5E7EB;
  --sidebar-ring: #3B82F6;
}

.dark {
  --background: #0F172A;
  --foreground: #F1F5F9;
  --card: #1E293B;
  --card-foreground: #F1F5F9;
  --popover: #1E293B;
  --popover-foreground: #F1F5F9;
  --primary: #3B82F6;
  --primary-foreground: #FFFFFF;
  --secondary: #1E293B;
  --secondary-foreground: #F1F5F9;
  --muted: #1E293B;
  --muted-foreground: #94A3B8;
  --accent: #1E293B;
  --accent-foreground: #F1F5F9;
  --destructive: #EF4444;
  --border: #334155;
  --input: #334155;
  --ring: #3B82F6;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: #1E293B;
  --sidebar-foreground: #F1F5F9;
  --sidebar-primary: #3B82F6;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #F1F5F9;
  --sidebar-border: #334155;
  --sidebar-ring: #3B82F6;
}

/* 基础样式 */
body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background-color: white;
  color: #111827;
}

/* 自定义滚动条 */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(156 163 175);
  }
}

/* 加载动画 */
@keyframes dot-flashing {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.2;
  }
  100% {
    opacity: 1;
  }
}

.dot-flashing {
  animation: dot-flashing 1.4s infinite linear alternate;
}

.dot-flashing:nth-child(2) {
  animation-delay: 0.2s;
}

.dot-flashing:nth-child(3) {
  animation-delay: 0.4s;
}

/* 骨架屏动画 */
@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.skeleton {
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: rgb(229 231 235);
  border-radius: 0.375rem;
}

/* 打字机效果 */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typing-effect {
  overflow: hidden;
  border-right: 2px solid #3B82F6;
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: #3B82F6;
  }
}

/* 增强 prose 样式 */
.prose {
  color: #111827;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: #111827;
  font-weight: 600;
}

.prose p {
  color: #111827;
  line-height: 1.625;
}

.prose a {
  color: #2563eb;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.prose a:hover {
  text-decoration: underline;
}

.prose strong {
  color: #111827;
  font-weight: 600;
}

.prose em {
  color: #111827;
  font-style: italic;
}

.prose code {
  color: #2563eb;
  background-color: #f3f4f6;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  border: 1px solid #e5e7eb;
}

.prose pre {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.prose pre code {
  background-color: transparent;
  border: 0;
  padding: 0;
}

.prose blockquote {
  color: #6b7280;
  border-left: 4px solid #bfdbfe;
  padding-left: 1.5rem;
  font-style: italic;
  background-color: #f9fafb;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0 0.25rem 0.25rem 0;
}

.prose ul,
.prose ol {
  color: #111827;
}

.prose li {
  color: #111827;
  line-height: 1.625;
}

.prose img {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  max-width: 100%;
  height: auto;
}

.prose hr {
  border-color: #e5e7eb;
  margin: 2rem 0;
  border-top-width: 2px;
}

.prose table {
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.prose th {
  border: 1px solid #e5e7eb;
  background-color: #f3f4f6;
  padding: 0.75rem;
  font-weight: 600;
  text-align: left;
}

.prose td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  vertical-align: top;
}
