'use client'

import { useState } from 'react'
import { WelcomeScreen } from '@/components/WelcomeScreen'
import { TopNavigation } from '@/components/TopNavigation'
import { ContentViewer } from '@/components/ContentViewer'
import { Sidebar } from '@/components/Sidebar'
import { AIChat } from '@/components/AIChat'

interface KnowledgeCard {
  title: string
  content: string
}

interface ProcessedContent {
  originalContent: string
  knowledgeCards: KnowledgeCard[]
  title: string
  cached?: boolean
}

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
}

type ViewMode = 'welcome' | 'workbench' | 'chat'

export default function Home() {
  const [viewMode, setViewMode] = useState<ViewMode>('welcome')
  const [currentUrl, setCurrentUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [processedContent, setProcessedContent] = useState<ProcessedContent | null>(null)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [chatLoading, setChatLoading] = useState(false)

  const handleProcessInput = async (input: string) => {
    setCurrentUrl(input)
    setLoading(true)
    setViewMode('workbench')

    try {
      const response = await fetch('/api/process-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: input }),
      })

      if (!response.ok) {
        throw new Error('Failed to process input')
      }

      const result: ProcessedContent = await response.json()
      setProcessedContent(result)
      setChatMessages([]) // 清空聊天记录
    } catch (error) {
      console.error('Error:', error)
      // 可以添加 toast 提示
      alert('处理输入时出错，请检查输入内容是否有效')
      setViewMode('welcome')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveCard = async (card: KnowledgeCard) => {
    try {
      const response = await fetch('/api/save-card', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: card.title,
          content: card.content,
          sourceUrl: currentUrl,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to save card')
      }
      // 成功保存，不需要额外操作，组件内部会处理状态更新
    } catch (error) {
      console.error('Error:', error)
      throw error // 重新抛出错误，让组件处理
    }
  }

  const handleSendMessage = async (message: string) => {
    if (!processedContent) return

    setChatLoading(true)
    const userMessage: ChatMessage = { role: 'user', content: message }
    setChatMessages(prev => [...prev, userMessage])

    try {
      const response = await fetch('/api/ask-question', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: message,
          context: processedContent.originalContent,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get answer')
      }

      const result = await response.json()
      const assistantMessage: ChatMessage = { role: 'assistant', content: result.answer }
      setChatMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error:', error)
      const errorMessage: ChatMessage = { 
        role: 'assistant', 
        content: '抱歉，回答问题时出错了。请稍后再试。' 
      }
      setChatMessages(prev => [...prev, errorMessage])
    } finally {
      setChatLoading(false)
    }
  }

  const handleLogoClick = () => {
    setViewMode('welcome')
    setProcessedContent(null)
    setChatMessages([])
    setCurrentUrl('')
  }

  const handleSyncClick = async () => {
    // 实现同步知识库的逻辑
    // 这里可以调用同步API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟异步操作
  }

  // 欢迎页
  if (viewMode === 'welcome') {
    return (
      <WelcomeScreen
        onSubmit={handleProcessInput}
        loading={loading}
      />
    )
  }

  // 知识库对话页面
  if (viewMode === 'chat') {
    return (
      <div className="min-h-screen bg-background">
        <TopNavigation
          onLogoClick={handleLogoClick}
          onChatClick={() => setViewMode('workbench')}
          onSyncClick={handleSyncClick}
        />
        <div className="h-[calc(100vh-4rem)] max-w-4xl mx-auto p-4">
          <div className="h-full bg-white rounded-lg border border-border">
            <div className="h-full p-6">
              <div className="mb-6">
                <h1 className="text-2xl font-semibold text-foreground">知识库对话</h1>
                <p className="text-muted-foreground mt-2">
                  与您的个人知识库进行智能对话
                </p>
              </div>
              <div className="h-[calc(100%-5rem)]">
                <AIChat
                  messages={chatMessages}
                  loading={chatLoading}
                  onSendMessage={handleSendMessage}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 主工作台 - 三面板布局
  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 左面板：文章标题和导航 */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* 顶部导航 */}
        <div className="p-4 border-b border-gray-200">
          <button
            onClick={handleLogoClick}
            className="flex items-center space-x-3 w-full text-left hover:bg-gray-50 p-2 rounded-lg transition-colors duration-200"
          >
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-semibold text-sm">知</span>
            </div>
            <span className="font-semibold text-gray-900">知识卡片</span>
          </button>
        </div>

        {/* 文章标题区域 */}
        <div className="flex-1 p-4">
          {processedContent ? (
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-2">
                {processedContent.title || '文章标题'}
              </h2>
              <p className="text-sm text-gray-600 mb-4">
                {currentUrl ? new URL(currentUrl).hostname : '本地内容'}
              </p>

              {/* 文章统计信息 */}
              <div className="space-y-2 text-sm text-gray-500">
                <div className="flex justify-between">
                  <span>知识卡片</span>
                  <span>{processedContent.knowledgeCards?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span>字数统计</span>
                  <span>{processedContent.originalContent?.length || 0}</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-500 mt-8">
              <div className="w-12 h-12 bg-gray-100 rounded-lg mx-auto mb-3 flex items-center justify-center">
                <span className="text-gray-400">📄</span>
              </div>
              <p>正在加载文章...</p>
            </div>
          )}
        </div>
      </div>

      {/* 中间面板：文章内容 */}
      <div className="flex-1 bg-white border-r border-gray-200">
        <ContentViewer
          content={processedContent}
          loading={loading}
        />
      </div>

      {/* 右面板：知识卡片和AI聊天 */}
      <div className="w-96 bg-white flex flex-col">
        <Sidebar
          knowledgeCards={processedContent?.knowledgeCards || []}
          knowledgeCardsLoading={loading}
          chatMessages={chatMessages}
          chatLoading={chatLoading}
          onSaveCard={handleSaveCard}
          onSendMessage={handleSendMessage}
        />
      </div>
    </div>
  )
}
