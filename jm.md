好的，我们来用文字精准地描绘出 "Augmented Reader" MVP 的前端设计。

这份设计稿将遵循简洁、直观、聚焦的原则，旨在用最少的视觉元素，引导用户完成核心操作，并营造一种专业、高效的“沉浸式阅读”氛围。我们将从整体布局开始，逐步深入到每个组件的细节和交互动态。

增强型阅读器 (Augmented Reader) - 前端设计语言
核心设计理念: “宁静致远 (Calm Tech)”

色调: 以中性色为主。大面积使用白色 (#FFFFFF)或极浅的灰色 (#F9FAFB)作为背景，营造开阔、无压力的空间感。主色调选用一种饱和度不高但清晰的蓝色 (#3B82F6)或靛蓝色 (#4F46E5)，用于按钮、链接、高亮和焦点状态，起到引导作用。文本为深灰色 (#1F2937)，保证最佳阅读性。
**字体:**选用无衬线字体。Inter 或系统默认字体 (SF Pro, Segoe UI, Roboto) 都是绝佳选择，能提供清晰、现代的阅读体验。
间距: 大量使用留白。组件之间、文本行与行之间保持足够的间距，避免信息拥挤，让用户的视线可以轻松流动。遵循 8px 网格系统 (间距、内外边距为 8, 16, 24, 32px)。
动效: 克制而有意义。动效仅用于状态过渡（如加载、成功）和提供反馈，避免无意义的炫技。动画曲线多采用缓入缓出 (ease-in-out)，感觉自然不突兀。
1. 初始启动页 (The Welcome Screen)
布局: 屏幕垂直水平居中。
元素:
Logo (可选): 顶部是一个简约的 Logo。
标题 (h1): 一行大字体的欢迎语，如 “增强阅读，即刻开始”。
输入框 (Input): 核心交互元素。一个宽度较大、高度适中（约 44-48px）的输入框。内部有占位符文字 粘贴一篇文章或一个网页链接...。当用户聚焦输入框时，边框会泛起主色调的辉光。
处理按钮 (Button): 输入框右侧紧邻一个方形按钮，图标为一个“闪电”⚡️或“魔术棒”✨，没有文字。当输入框有内容时，此按钮变为激活状态（主色调背景）。
交互: 用户粘贴链接或文本后，点击按钮或按回车，整个页面会平滑地（淡出淡入）过渡到主工作台界面。
2. 主工作台 (The Workbench)
这是一个常驻的两栏布局，是产品的核心。

A. 左侧 - 主体区 / 内容浏览器 (The Main Content Area)
背景: 纯白。
内容:
加载状态: 显示一个由三个小点组成的、优雅的加载动画 (dot-flashing)。
渲染后:
排版: 使用 @tailwindcss/typography 插件，自动为 h1-h6, p, ul, li 等HTML标签应用优美的、经过精心调校的印刷样式。这确保了即使用户粘贴的是无格式文本，显示出来也像一篇精心排版的博客文章。
滚动条: 一个纤细、不显眼的自定义滚动条。
B. 右侧 - 侧边栏 / AI 助理 (The AI Sidebar)
背景: 极浅的灰色 (#F9FAFB)，与左侧主体区在视觉上形成微妙的区分。
布局: 顶部是 Tabs，下方是对应的内容区域。
顶部 Tabs (切换器):
两个标签: “知识卡片” 和 “AI 问答”。
默认选中“知识卡片”。选中的标签下方会有一条主色调的细线作为指示器，标签文字变为高亮状态。切换时有平滑的动画效果。
“知识卡片” Tab 内容区:
加载状态: 显示多个卡片形状的“骨架屏 (Skeleton Screen)”，模拟即将加载出的内容，提升体验。
渲染后: 一个垂直的卡片流。
单张卡片 (Card):
结构: 遵循 shadcn/ui 的 Card 组件结构，有 CardHeader, CardContent, CardFooter。
Header: 左侧是卡片标题 (e.g., "核心摘要")，字体加粗。右侧是一个极简的“更多”图标 (三个点)，悬停时显示“复制内容”等选项。
Content: 卡片的主要内容文本。
Footer: 右下角是一个关键的 “+ 保存” 按钮。这是一个胶囊形状、带有图标和文字的按钮。
默认状态: 浅灰色背景，主色调文字和图标。
悬停状态 (Hover): 背景变为更深一点的灰色。
点击后 (成功保存): 按钮立即变为**“✓ 已保存”**，背景变为浅绿色，文字变为深绿色，并禁用点击。这个即时反馈非常重要。
“AI 问答” Tab 内容区:
对话历史区:
用户气泡: 右对齐，背景为主色调，文字为白色。
AI 气泡: 左对齐，背景为白色（或极浅灰色），有细微的边框。AI 的回答如果是流式输出，会有打字机效果，并显示一个闪烁的光标。
输入框区域:
位于侧边栏最底部。
一个可以随文字增多而自动增高的文本输入区 (textarea)。
右侧是一个圆形的“发送”按钮，图标为纸飞机 ✈️。当输入框有内容时，按钮变为激活状态（主色调背景）。
3. 顶部导航与全局操作 (Top Navigation & Global Actions)
在主工作台顶端，有一条纤细的水平导航栏。

左侧: 应用的 Logo 和名称。点击可刷新回到初始启动页。
右侧:
同步知识库按钮: 一个带有“同步”图标（如两个循环箭头 🔄）的按钮。点击后，图标开始旋转，并显示提示文字“同步中...”。完成后，图标停止旋转，并短暂显示“同步完成 ✓”。
对话按钮: 一个文字按钮 “知识库对话”。点击后，平滑地跳转到全屏的对话页面。
4. 知识库对话页 (The Chat Page)
布局: 全屏、居中的对话界面，非常类似一个独立的聊天应用。
集成方式:
如果用 iframe: 页面主体就是一个占满全屏的 iframe，加载 FastGPT 的对话窗口。
如果用 API: 复用侧边栏“AI 问答”的组件，但将其放大至全屏尺寸，提供更沉浸的对话体验。
这份文字设计稿为您描绘了一个完整、连贯且注重细节的前端体验。它将确保您的产品不仅功能强大，而且在视觉和交互上都给人一种信赖、高效和愉悦的感觉。
